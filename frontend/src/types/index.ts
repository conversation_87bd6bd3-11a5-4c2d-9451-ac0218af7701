// 歌舞伎台本の行タイプ
export type LineType = "normal" | "iori" | "tobogaki" | "actor" | "empty";

// 解析結果の行データ
export interface ScriptLine {
  line_id: string;
  line_type: LineType;
  actor?: string;
  text: string;
  x?: number;
  y?: number;
  confidence?: number;
  has_unreadable_chars: boolean;
  original_text?: string;
}

// PDFページデータ
export interface PDFPageData {
  page_number: number;
  image_url: string;
  width: number;
  height: number;
}

// 画像選択データ
export interface ImageSelectionData {
  x: number;
  y: number;
  width: number;
  height: number;
}

// Vision API解析結果
export interface VisionAnalysisResult {
  lines: ScriptLine[];
  confidence: number;
  processing_time: number;
}

// レイアウトルール
export interface LayoutRule {
  type: LineType;
  indentChars: number;
  alignment: "left" | "right" | "center";
  prefix?: string;
}

// Word文書生成オプション
export interface WordGenerationOptions {
  charactersPerLine: number;
  verticalWriting: boolean;
  fontSize: number;
  fontFamily: string;
}

// 認証関連
export interface LoginRequest {
  username: string;
  password: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  is_active: boolean;
  created_at: string;
}

export interface AuthToken {
  access_token: string;
  token_type: string;
}

export interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (username: string, password: string) => Promise<void>;
  refresh: () => Promise<void>;
  logout: () => void;
  isLoading: boolean;
}
