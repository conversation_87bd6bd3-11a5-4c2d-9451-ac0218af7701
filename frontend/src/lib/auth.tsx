"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { User, AuthContextType } from "@/types";

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// APIのベースURLを環境に応じて設定
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    console.log("AuthProvider: Initializing...");
    // ページロード時にローカルストレージからトークンを取得
    const savedToken = localStorage.getItem("token");
    const savedUser = localStorage.getItem("user");

    console.log("AuthProvider: Saved token exists:", !!savedToken);
    console.log("AuthProvider: Saved user exists:", !!savedUser);

    if (savedToken && savedUser) {
      try {
        const parsedUser = JSON.parse(savedUser);
        setToken(savedToken);
        setUser(parsedUser);
        console.log(
          "AuthProvider: Restored user session:",
          parsedUser.username
        );
      } catch (error) {
        console.error("AuthProvider: Error parsing saved user:", error);
        localStorage.removeItem("token");
        localStorage.removeItem("user");
      }
    }

    setIsLoading(false);
    console.log("AuthProvider: Initialization complete");
  }, []);

  const login = async (username: string, password: string) => {
    try {
      console.log("Login: Starting login process...");
      console.log("Login: API_BASE_URL:", API_BASE_URL);
      console.log("Login: Username:", username);

      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: "POST",
        credentials: "include",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: new URLSearchParams({
          username,
          password,
        }),
      });

      console.log("Login: Response status:", response.status);
      console.log(
        "Login: Response headers:",
        Object.fromEntries(response.headers.entries())
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Login: Error response body:", errorText);
        throw new Error(
          `ログインに失敗しました: ${response.status} ${response.statusText}`
        );
      }

      const tokenData = await response.json();
      console.log("Login: Token received successfully");

      // ユーザー情報を取得
      console.log("Login: Fetching user info...");
      const userResponse = await fetch(`${API_BASE_URL}/api/auth/me`, {
        headers: {
          Authorization: `Bearer ${tokenData.access_token}`,
        },
      });

      console.log("Login: User info response status:", userResponse.status);

      if (!userResponse.ok) {
        const userErrorText = await userResponse.text();
        console.error("Login: User info error response:", userErrorText);
        throw new Error("ユーザー情報の取得に失敗しました");
      }

      const userData = await userResponse.json();
      console.log("Login: User info received:", userData);

      // 状態とローカルストレージに保存
      setToken(tokenData.access_token);
      setUser(userData);
      localStorage.setItem("token", tokenData.access_token);
      localStorage.setItem("user", JSON.stringify(userData));

      console.log("Login: Login completed successfully");
    } catch (error) {
      console.error("Login: Error during login:", error);
      throw error;
    }
  };

  const refresh = async () => {
    try {
      console.log("Refresh: Refreshing token...");
      console.log("Refresh: API_BASE_URL:", API_BASE_URL);
      const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {
        method: "POST",
        credentials: "include",
      });

      console.log("Refresh: Response status:", response.status);
      console.log(
        "Refresh: Response headers:",
        Object.fromEntries(response.headers.entries())
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Refresh: Error response body:", errorText);
        throw new Error(
          `トークンのリフレッシュに失敗しました: ${response.status} ${response.statusText}`
        );
      }

      const tokenData = await response.json();
      console.log("Refresh: Token received successfully");

      // 状態とローカルストレージに保存
      setToken(tokenData.access_token);
      localStorage.setItem("token", tokenData.access_token);
      console.log("Refresh: Token refreshed successfully");
    } catch (error) {
      console.error("Refresh: Error during refresh:", error);
      throw error;
    }
  };

  const logout = () => {
    console.log("Logout: Clearing user session");
    setUser(null);
    setToken(null);
    localStorage.removeItem("token");
    localStorage.removeItem("user");
  };

  return (
    <AuthContext.Provider
      value={{ user, token, login, refresh, logout, isLoading }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
