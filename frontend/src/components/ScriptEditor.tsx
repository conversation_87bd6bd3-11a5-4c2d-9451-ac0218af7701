'use client'

import { useState, useCallback } from 'react'
import { ScriptLine, LineType } from '@/types'

interface ScriptEditorProps {
  lines: ScriptLine[]
  onChange: (lines: ScriptLine[]) => void
}

export default function ScriptEditor({ lines, onChange }: ScriptEditorProps) {
  const [selectedLineType, setSelectedLineType] = useState<LineType>('normal')
  const [isApplyingLayout, setIsApplyingLayout] = useState(false)

  const handleLineChange = useCallback((lineId: string, field: keyof ScriptLine, value: any) => {
    const updatedLines = lines.map(line => 
      line.line_id === lineId ? { ...line, [field]: value } : line
    )
    onChange(updatedLines)
  }, [lines, onChange])

  const handleAddLine = useCallback(() => {
    // より確実な一意ID生成
    const generateId = () => {
      const timestamp = Date.now()
      const random = Math.random().toString(36).substring(2, 15)
      return `line-${timestamp}-${random}`
    }
    
    const newLine: ScriptLine = {
      line_id: generateId(),
      line_type: selectedLineType,
      text: '',
      has_unreadable_chars: false,
    }
    onChange([...lines, newLine])
  }, [lines, onChange, selectedLineType])

  const handleDeleteLine = useCallback((lineId: string) => {
    // 削除前の安全チェック
    if (!lineId || lines.length === 0) {
      console.warn('削除対象の行が見つかりません')
      return
    }
    
    const updatedLines = lines.filter(line => line.line_id !== lineId)
    
    // 削除後の配列が空でないことを確認
    if (updatedLines.length === 0) {
      console.log('すべての行が削除されました')
    }
    
    onChange(updatedLines)
  }, [lines, onChange])

  const handleApplyLayout = useCallback(async () => {
    if (isApplyingLayout) return

    setIsApplyingLayout(true)
    try {
      const response = await fetch('/api/apply-layout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ lines }),
      })

      if (!response.ok) {
        throw new Error('レイアウト適用に失敗しました')
      }

      const { lines: layoutAppliedLines } = await response.json()
      onChange(layoutAppliedLines)
    } catch (error) {
      console.error('レイアウト適用エラー:', error)
      alert('レイアウトの適用に失敗しました。')
    } finally {
      setIsApplyingLayout(false)
    }
  }, [lines, onChange, isApplyingLayout])

  const getLineTypeLabel = (type: LineType) => {
    switch (type) {
      case 'normal': return '通常'
      case 'iori': return 'いおり'
      case 'tobogaki': return 'ト書'
      case 'actor': return '役者'
      case 'empty': return '空行'
      default: return '通常'
    }
  }

  if (lines.length === 0) {
    return (
      <div className="text-center py-12 text-muted-foreground">
        <div className="text-4xl mb-4">📝</div>
        <p>PDFを解析すると、編集可能なテキストがここに表示されます</p>
        <p className="text-sm mt-2">「解析開始」ボタンをクリックしてください</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* 操作ボタン */}
      <div className="flex justify-center gap-4">
        <button
          onClick={handleAddLine}
          className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 font-medium"
        >
          + 行追加
        </button>
        <button
          onClick={handleApplyLayout}
          disabled={isApplyingLayout}
          className={`px-4 py-2 rounded-lg font-medium transition-colors ${
            isApplyingLayout
              ? 'bg-muted text-muted-foreground cursor-not-allowed'
              : 'bg-green-600 text-white hover:bg-green-700'
          }`}
        >
          {isApplyingLayout ? '適用中...' : '📐 スタイル適用'}
        </button>
      </div>

      {/* 縦書きグリッド */}
      <div className="border border-border rounded-lg overflow-hidden bg-muted">
        <div className="max-h-[700px] overflow-auto">
          {/* 縦書きグリッド：右から左へ列が並ぶ */}
          <div className="flex flex-row-reverse gap-3 p-6 min-w-max">
            {lines.map((line, index) => {
              // デバッグ: 重複IDチェック
              const duplicateIds = lines.filter(l => l.line_id === line.line_id)
              if (duplicateIds.length > 1) {
                console.warn(`重複ID発見: ${line.line_id} (${duplicateIds.length}個)`)
              }
              
              return (
              <div
                key={`${line.line_id}-${index}`}
                className={`flex flex-col border-2 rounded-lg p-2 min-h-[600px] w-24 shadow-sm ${
                  line.has_unreadable_chars ? 'bg-yellow-500/10 border-yellow-500/30' : 'bg-card border-border'
                }`}
              >
                {/* 列番号 */}
                <div className="text-sm text-muted-foreground mb-2 text-center font-bold bg-secondary rounded py-1">
                  {index + 1}
                </div>

                {/* 役者名（縦書き） */}
                <div className="mb-3">
                  <textarea
                    value={line.actor || ''}
                    onChange={(e) => handleLineChange(line.line_id, 'actor', e.target.value)}
                    className="w-full h-16 text-sm border-2 rounded-md px-1 py-1 resize-none font-medium bg-primary/10 border-primary/20 focus:border-primary focus:outline-none text-card-foreground"
                    placeholder="役者名"
                    style={{
                      writingMode: 'vertical-rl',
                      textOrientation: 'upright',
                      lineHeight: '1.4'
                    }}
                  />
                </div>

                {/* セリフ（縦書き） */}
                <div className="flex-1 mb-2">
                  <div className="relative">
                    <textarea
                      value={line.text}
                      onChange={(e) => handleLineChange(line.line_id, 'text', e.target.value)}
                      className={`w-full h-80 text-sm border-2 rounded-md px-2 py-2 resize-none font-medium text-card-foreground ${
                        line.has_unreadable_chars ? 'bg-yellow-500/10 border-yellow-500/30' : 'bg-green-500/10 border-green-500/20'
                      } focus:outline-none focus:border-green-500`}
                      placeholder="セリフ"
                      style={{
                        writingMode: 'vertical-rl',
                        textOrientation: 'upright',
                        lineHeight: '1.6',
                        letterSpacing: '0.05em'
                      }}
                    />
                    {line.has_unreadable_chars && (
                      <div className="absolute -top-1 -right-1 bg-yellow-500 text-white text-xs px-1 py-0.5 rounded-full">
                        ⚠️
                      </div>
                    )}
                  </div>
                </div>

                {/* タイプ表示（小さく） */}
                <div className="mb-2">
                  <div className="text-xs text-muted-foreground text-center bg-secondary rounded px-1 py-1">
                    {getLineTypeLabel(line.line_type)}
                  </div>
                </div>

                {/* 削除ボタン */}
                <div>
                  <button
                    onClick={() => handleDeleteLine(line.line_id)}
                    className="w-full text-xs text-destructive hover:text-destructive-foreground hover:bg-destructive border border-destructive/30 hover:border-destructive rounded-md py-1 transition-colors"
                  >
                    削除
                  </button>
                </div>
              </div>
            )
            })}
          </div>
        </div>
      </div>

    </div>
  )
}
